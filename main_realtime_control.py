"""
Revo2灵巧手实时控制主程序

本程序实现了Revo2灵巧手的实时控制功能，包括：
- 自动检测并连接串口设备
- 实时接收外部控制数据
- 根据接收到的数据控制机械手动作
- 实时监控机械手状态
- 支持多种控制模式（位置、速度、电流）

使用方法：
1. 确保Revo2设备已正确连接到计算机
2. 运行程序：python main_realtime_control.py
3. 程序会自动检测设备并建立连接
4. 通过键盘输入或外部数据源控制机械手

控制命令格式：
- 位置控制：pos [thumb] [index] [middle] [ring] [pinky] [wrist]
- 速度控制：speed [thumb] [index] [middle] [ring] [pinky] [wrist]
- 电流控制：current [thumb] [index] [middle] [ring] [pinky] [wrist]
- 状态查询：status
- 退出程序：quit
"""

import asyncio
import sys
import json
import threading
import time
from typing import List, Optional, Tuple
from revo2_utils import libstark, logger, open_modbus_revo2


class Revo2RealtimeController:
    """Revo2灵巧手实时控制器"""
    
    def __init__(self):
        self.client = None
        self.slave_id = None
        self.is_connected = False
        self.is_running = False
        self.control_mode = "position"  # position, speed, current
        self.last_positions = [0] * 6
        self.last_speeds = [0] * 6
        self.last_currents = [0] * 6
        
    async def initialize(self, port_name: Optional[str] = None):
        """
        初始化连接到Revo2设备
        
        Args:
            port_name: 串口名称，None表示自动检测
        """
        try:
            logger.info("正在连接Revo2设备...")
            (self.client, self.slave_id) = await open_modbus_revo2(port_name=port_name)
            
            # 配置控制模式为千分比模式
            await self.client.set_finger_unit_mode(self.slave_id, libstark.FingerUnitMode.Normalized)
            
            # 获取设备信息
            device_info = await self.client.get_device_info(self.slave_id)
            logger.info(f"设备连接成功: {device_info.description}")
            
            self.is_connected = True
            return True
            
        except Exception as e:
            logger.error(f"设备连接失败: {e}")
            return False
    
    async def get_motor_status(self) -> Optional[libstark.MotorStatusData]:
        """获取电机状态"""
        if not self.is_connected:
            return None
            
        try:
            status = await self.client.get_motor_status(self.slave_id)
            return status
        except Exception as e:
            logger.error(f"获取电机状态失败: {e}")
            return None
    
    async def set_finger_positions(self, positions: List[int], durations: List[int] = None):
        """
        设置手指位置
        
        Args:
            positions: 6个手指的目标位置 [拇指, 食指, 中指, 无名指, 小指, 手腕]
            durations: 到达目标位置的时间（毫秒），默认为300ms
        """
        if not self.is_connected:
            logger.warning("设备未连接")
            return False
            
        try:
            if durations is None:
                durations = [300] * 6
                
            await self.client.set_finger_positions_and_durations(
                self.slave_id, positions, durations
            )
            self.last_positions = positions.copy()
            return True
            
        except Exception as e:
            logger.error(f"设置手指位置失败: {e}")
            return False
    
    async def set_finger_speeds(self, speeds: List[int]):
        """
        设置手指速度
        
        Args:
            speeds: 6个手指的速度值 [-1000 ~ 1000]
        """
        if not self.is_connected:
            logger.warning("设备未连接")
            return False
            
        try:
            await self.client.set_finger_speeds(self.slave_id, speeds)
            self.last_speeds = speeds.copy()
            return True
            
        except Exception as e:
            logger.error(f"设置手指速度失败: {e}")
            return False
    
    async def set_finger_currents(self, currents: List[int]):
        """
        设置手指电流
        
        Args:
            currents: 6个手指的电流值 [-1000 ~ 1000]
        """
        if not self.is_connected:
            logger.warning("设备未连接")
            return False
            
        try:
            await self.client.set_finger_currents(self.slave_id, currents)
            self.last_currents = currents.copy()
            return True
            
        except Exception as e:
            logger.error(f"设置手指电流失败: {e}")
            return False
    
    async def emergency_stop(self):
        """紧急停止所有手指"""
        logger.warning("执行紧急停止")
        await self.set_finger_speeds([0] * 6)
    
    def disconnect(self):
        """断开连接"""
        if self.client:
            libstark.modbus_close(self.client)
            self.is_connected = False
            logger.info("设备连接已断开")


class CommandProcessor:
    """命令处理器"""
    
    def __init__(self, controller: Revo2RealtimeController):
        self.controller = controller
        
    def parse_command(self, command: str) -> Tuple[str, List[float]]:
        """
        解析控制命令
        
        Args:
            command: 输入命令字符串
            
        Returns:
            (command_type, values): 命令类型和参数值列表
        """
        parts = command.strip().split()
        if not parts:
            return "invalid", []
            
        cmd_type = parts[0].lower()
        
        if cmd_type in ["pos", "position"]:
            if len(parts) != 7:
                return "invalid", []
            try:
                values = [float(x) for x in parts[1:]]
                # 限制位置值范围 0-1000
                values = [max(0, min(1000, v)) for v in values]
                return "position", values
            except ValueError:
                return "invalid", []
                
        elif cmd_type in ["speed", "vel"]:
            if len(parts) != 7:
                return "invalid", []
            try:
                values = [float(x) for x in parts[1:]]
                # 限制速度值范围 -1000 ~ 1000
                values = [max(-1000, min(1000, v)) for v in values]
                return "speed", values
            except ValueError:
                return "invalid", []
                
        elif cmd_type in ["current", "cur"]:
            if len(parts) != 7:
                return "invalid", []
            try:
                values = [float(x) for x in parts[1:]]
                # 限制电流值范围 -1000 ~ 1000
                values = [max(-1000, min(1000, v)) for v in values]
                return "current", values
            except ValueError:
                return "invalid", []
                
        elif cmd_type in ["status", "stat"]:
            return "status", []
            
        elif cmd_type in ["quit", "exit", "q"]:
            return "quit", []
            
        elif cmd_type in ["stop", "emergency"]:
            return "stop", []
            
        else:
            return "invalid", []
    
    async def execute_command(self, command: str) -> bool:
        """
        执行控制命令
        
        Args:
            command: 输入命令字符串
            
        Returns:
            bool: 是否继续运行程序
        """
        cmd_type, values = self.parse_command(command)
        
        if cmd_type == "position":
            positions = [int(v) for v in values]
            success = await self.controller.set_finger_positions(positions)
            if success:
                logger.info(f"位置设置成功: {positions}")
            else:
                logger.error("位置设置失败")
                
        elif cmd_type == "speed":
            speeds = [int(v) for v in values]
            success = await self.controller.set_finger_speeds(speeds)
            if success:
                logger.info(f"速度设置成功: {speeds}")
            else:
                logger.error("速度设置失败")
                
        elif cmd_type == "current":
            currents = [int(v) for v in values]
            success = await self.controller.set_finger_currents(currents)
            if success:
                logger.info(f"电流设置成功: {currents}")
            else:
                logger.error("电流设置失败")
                
        elif cmd_type == "status":
            status = await self.controller.get_motor_status()
            if status:
                logger.info(f"电机状态: {status.description}")
                logger.info(f"位置: {list(status.positions)}")
                logger.info(f"速度: {list(status.speeds)}")
                logger.info(f"电流: {list(status.currents)}")
            else:
                logger.error("获取状态失败")
                
        elif cmd_type == "stop":
            await self.controller.emergency_stop()
            logger.info("紧急停止执行完成")
            
        elif cmd_type == "quit":
            logger.info("程序退出")
            return False
            
        elif cmd_type == "invalid":
            logger.warning("无效命令格式")
            self.print_help()
            
        return True
    
    def print_help(self):
        """打印帮助信息"""
        help_text = """
控制命令格式：
- 位置控制：pos [拇指] [食指] [中指] [无名指] [小指] [手腕] (0-1000)
- 速度控制：speed [拇指] [食指] [中指] [无名指] [小指] [手腕] (-1000~1000)
- 电流控制：current [拇指] [食指] [中指] [无名指] [小指] [手腕] (-1000~1000)
- 状态查询：status
- 紧急停止：stop
- 退出程序：quit

示例：
pos 500 500 500 500 500 500    # 所有手指移动到中间位置
speed 100 0 0 0 0 0           # 拇指正向运动，其他手指停止
current -200 -200 -200 -200 -200 -200  # 所有手指反向施力
        """
        print(help_text)


async def main():
    """主函数"""
    print("=== Revo2灵巧手实时控制程序 ===")
    print("正在初始化...")
    
    # 创建控制器
    controller = Revo2RealtimeController()
    
    # 初始化连接
    if not await controller.initialize():
        print("设备连接失败，程序退出")
        sys.exit(1)
    
    # 创建命令处理器
    processor = CommandProcessor(controller)
    
    print("设备连接成功！")
    print("输入 'help' 查看命令格式，输入 'quit' 退出程序")
    processor.print_help()
    
    try:
        # 主控制循环
        while True:
            try:
                # 获取用户输入
                command = input("\n请输入控制命令: ").strip()
                
                if command.lower() == "help":
                    processor.print_help()
                    continue
                
                # 执行命令
                should_continue = await processor.execute_command(command)
                if not should_continue:
                    break
                    
            except KeyboardInterrupt:
                print("\n检测到Ctrl+C，正在安全退出...")
                await controller.emergency_stop()
                break
            except EOFError:
                print("\n输入结束，正在退出...")
                break
            except Exception as e:
                logger.error(f"命令执行错误: {e}")
                
    finally:
        # 清理资源
        controller.disconnect()
        print("程序已安全退出")


if __name__ == "__main__":
    asyncio.run(main())
